"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Home,
  LogOut,
  User,
  ShoppingCart,
  Search,
  Container,
  Shield,
  Smartphone,
  Shirt,
  Wrench,
  Baby,
  PartyPopper,
  Coffee,
  Palette,
  FileText,
  Heart,
  ChevronRight,
} from "lucide-react";
import { useCart } from "@/lib/CartContext";
import { useAuth } from "@/hooks/use-auth";
import { AuthService } from "@/services/auth.service";
import { categories } from "@/lib/constants";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertD<PERSON>ogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTrigger,
} from "@/components/ui/sheet";
import { UserService } from "@/services/user.service";

export default function Header(): JSX.Element {
  // Use the auth hook for authentication state
  const { loading: authLoading, isAuthenticated, isAdmin, user } = useAuth();

  // Prevent hydration mismatches by rendering a lightweight placeholder on first render.
  // We mark mounted only on the client in useEffect and render the full header afterwards.
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  const pathname = usePathname();
  const router = useRouter();
  const cart = useCart();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(),
  );
  const authService = new AuthService();
  const userService = new UserService();

  const [userData, setUserData] = useState<{
    first_name?: string;
    last_name?: string;
    email?: string;
  } | null>(null);

  const toggleMenu = () => {
    setIsMenuOpen((s) => !s);
  };

  const toggleProfileDropdown = () => {
    setIsProfileDropdownOpen((s) => !s);
  };

  const handleSignout = async () => {
    await authService.signOut();
  };

  const isActivePath = (path: string) =>
    pathname === path
      ? "text-primary font-semibold"
      : "text-gray-700 hover:text-primary";

  const fetchUser = async (): Promise<{first_name: string, last_name: string, email: string} | null> => {
    try {
      const profile = await userService.getUserProfile();
      return profile;
    } catch (e) {
      console.error("Error fetching user:", e);
      return null;
    }
  }


  // Fetch user data when authenticated
  // Note: isAuthenticated is derived from localStorage in the auth hook.
  // We should read localStorage whenever `isAuthenticated` becomes true so initials
  // are available even if `user` (from Supabase) is not present yet.
  useEffect(() => {
    if (isAuthenticated) {
      // Fetch user data from localStorage as a fallback for display purposes
      // let authUser = null;
      fetchUser().then((user) => {
        setUserData(user);
      });
      // if (authUser) {
      //   try {
      //     const parsedUser = JSON.parse(authUser);
      //     setUserData(parsedUser);
      //   } catch {
      //     setUserData(null);
      //   }
      // } else {
      //   setUserData(null);
      // }
    } else {
      setUserData(null);
    }
  }, [isAuthenticated]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (
        isProfileDropdownOpen &&
        !target.closest(".profile-dropdown-container") &&
        !target.closest('[role="alertdialog"]') &&
        !target.closest('[role="dialog"]')
      ) {
        setIsProfileDropdownOpen(false);
      }
    };

    if (isProfileDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isProfileDropdownOpen]);

  const getDisplayInitials = () => {
    if (userData?.first_name || userData?.last_name) {
      return `${userData?.first_name?.[0] ?? ""}${userData?.last_name?.[0] ?? ""}`;
    }

    // Guard against SSR where `document` is not available
    if (typeof document === "undefined") {
      return "";
    }

    // Try to read user info from localStorage as a fallback (covers cases where
    // isAuthenticated is true but `user` from Supabase might be null)
    try {
      const authUser =
        typeof window !== "undefined"
          ? localStorage.getItem("__mp_user_data")
          : null;
      if (authUser) {
        const parsedUser = JSON.parse(authUser);
        if (parsedUser?.first_name || parsedUser?.last_name) {
          return `${parsedUser?.first_name?.[0] ?? ""}${parsedUser?.last_name?.[0] ?? ""}`;
        }
      }
    } catch {
      // ignore parse errors and continue to admin cookie fallback
    }

    const adminCookie = document.cookie
      .split("; ")
      .find((row) => row.startsWith("_auth_admin="));
    if (adminCookie) {
      try {
        const encoded = adminCookie.split("=")[1] ?? "";
        // decodeURIComponent in case cookie was encoded
        const admin = JSON.parse(decodeURIComponent(encoded));
        return `${admin?.first_name?.[0] ?? ""}${admin?.last_name?.[0] ?? ""}`;
      } catch {
        return "";
      }
    }
    return "";
  };

  const displayInitials = getDisplayInitials();

  const handleSearch = (shouldBlur = false) => {
    if (searchValue.trim()) {
      router.push(
        `/home/<USER>/results?query=${encodeURIComponent(searchValue.trim())}`,
      );

      // Hide keyboard on mobile after search
      if (shouldBlur && document.activeElement) {
        (document.activeElement as HTMLElement).blur();
      }
    }
  };

  const cartQuantity =
    cart.items?.reduce((total, item) => total + item.quantity, 0) ?? 0;

  // Category hierarchy types and functions
  type Category = {
    id: string;
    name: string;
    parent_id: string | null;
  };

  type CategoryHierarchy = {
    id: string;
    name: string;
    children: CategoryHierarchy[];
  };

  // Build category hierarchy from flat categories array
  const buildCategoryHierarchy = (
    categories: Category[],
  ): CategoryHierarchy[] => {
    const categoryMap = new Map<string, CategoryHierarchy>();
    const rootCategories: CategoryHierarchy[] = [];

    // First pass: create all category objects
    categories.forEach((category) => {
      categoryMap.set(category.id, {
        id: category.id,
        name: category.name,
        children: [],
      });
    });

    // Second pass: build hierarchy
    categories.forEach((category) => {
      const categoryNode = categoryMap.get(category.id)!;
      if (category.parent_id === null) {
        rootCategories.push(categoryNode);
      } else {
        const parent = categoryMap.get(category.parent_id);
        if (parent) {
          parent.children.push(categoryNode);
        }
      }
    });

    return rootCategories;
  };

  // Toggle category expansion
  const toggleCategoryExpansion = (id: string) => {
    const next = new Set(expandedCategories);
    if (next.has(id)) {
      next.delete(id);
    } else {
      next.add(id);
    }
    setExpandedCategories(next);
  };

  // Reset expanded categories when menu closes
  useEffect(() => {
    if (!isMenuOpen) {
      setExpandedCategories(new Set());
    }
  }, [isMenuOpen]);

  // Build the category hierarchy
  const categoryHierarchy = buildCategoryHierarchy(categories);

  // Function to get icon for category
  // const getCategoryIcon = (categoryName: string) => {
  //   const iconMap: { [key: string]: React.ReactNode } = {
  //     Electronics: <Smartphone className="w-5 h-5 text-blue-600" />,
  //     "Fashion & Apparel": <Shirt className="w-5 h-5 text-pink-600" />,
  //     "Tools & DIY": <Wrench className="w-5 h-5 text-orange-600" />,
  //     "Baby & Kids": <Baby className="w-5 h-5 text-green-600" />,
  //     "Party Supplies": <PartyPopper className="w-5 h-5 text-purple-600" />,
  //     "Groceries & Gourmet Food": <Coffee className="w-5 h-5 text-amber-600" />,
  //     "Arts, Crafts & Sewing": <Palette className="w-5 h-5 text-red-600" />,
  //     "Office Supplies": <FileText className="w-5 h-5 text-gray-600" />,
  //     "Health & Beauty": <Heart className="w-5 h-5 text-rose-600" />,
  //   };
  //   return (
  //     iconMap[categoryName] || <Container className="w-5 h-5 text-gray-500" />
  //   );
  // };

  // If the component hasn't mounted on the client yet or auth is loading, return a stable, minimal placeholder
  // so the server HTML and initial client HTML have the same shape and avoid hydration errors.
  if (!mounted || authLoading) {
    return (
      <header className="sticky top-0 z-50 bg-white shadow-md" aria-hidden>
        <div className="h-[60px]" />
      </header>
    );
  }

  return (
    <header className="sticky top-0 z-50 bg-white shadow-md">
      {/* Desktop Layout */}
      <div className="hidden sm:flex items-center px-4 sm:px-[8%] py-3 h-[60px]">
        {/* Left: Logo */}
        <div className="flex items-center">
          <a href="/" onClick={() => setIsMenuOpen(false)}>
            <img
              src="/assets/imgs/logo.svg"
              alt="mailpallet-logo"
              width={120}
              height={40}
              className="h-auto"
            />
          </a>
        </div>

        {/* Center: Search Bar (hide on specific pages) */}
        {pathname !== "/login" &&
          pathname !== "/signup" &&
          pathname !== "/home/<USER>" &&
          pathname !== "/home/<USER>/my-orders" &&
          pathname !== "/home/<USER>" && (
            <div className="flex-1 flex justify-center mx-8">
              <div className="flex items-center w-full max-w-2xl">
                <Input
                  type="text"
                  placeholder="Search for products"
                  className="flex-1 mr-2"
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch(false)}
                />
                <Button
                  className="bg-primary hover:bg-blue-800 text-white"
                  onClick={() => handleSearch()}
                >
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
              </div>
            </div>
          )}

        {/* Add flex-1 spacer when search is hidden to push buttons to the right */}
        {(pathname === "/login" ||
          pathname === "/signup" ||
          pathname === "/home/<USER>" ||
          pathname === "/home/<USER>/my-orders" ||
          pathname === "/home/<USER>") && <div className="flex-1"></div>}

        {/* Right: Profile/Login and cart (hide cart on login page) */}
        <div className="flex items-center space-x-4">
          {isAuthenticated ? (
            <>
              <div className="relative profile-dropdown-container">
                <div
                  onClick={toggleProfileDropdown}
                  className="cursor-pointer"
                  role="button"
                  tabIndex={0}
                  aria-expanded={isProfileDropdownOpen}
                  onKeyDown={(e) =>
                    e.key === "Enter" && toggleProfileDropdown()
                  }
                >
                  <Avatar className="w-10 h-10 hover:ring-2 hover:ring-blue-300 transition-all">
                    <AvatarFallback className="bg-blue-600 text-white font-bold text-sm">
                      {displayInitials}
                    </AvatarFallback>
                  </Avatar>
                </div>

                {isProfileDropdownOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-100 z-50">
                    <div className="space-y-1 p-2">
                      <Link
                        href="/home/<USER>/my-orders"
                        className="flex items-center space-x-3 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                        onClick={() => setIsProfileDropdownOpen(false)}
                      >
                        <Container className="w-4 h-4 text-gray-600" />
                        <span className="text-sm">My Orders</span>
                      </Link>

                      {/* <Link
                        href="/home/<USER>"
                        className="flex items-center space-x-3 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                        onClick={() => setIsProfileDropdownOpen(false)}
                      >
                        <Home className="w-4 h-4 text-gray-600" />
                        <span className="text-sm">Virtual Address</span>
                      </Link> */}

                      <Link
                        href="/home/<USER>"
                        className="flex items-center space-x-3 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                        onClick={() => setIsProfileDropdownOpen(false)}
                      >
                        <User className="w-4 h-4 text-gray-600" />
                        <span className="text-sm">Profile</span>
                      </Link>

                      {isAdmin && (
                        <Link
                          href="/admin/dashboard"
                          className="flex items-center space-x-3 p-2 rounded-lg hover:bg-green-50 transition-colors"
                          onClick={() => setIsProfileDropdownOpen(false)}
                        >
                          <Shield className="w-4 h-4 text-green-600" />
                          <span className="text-sm text-green-600 font-medium">
                            Admin Dashboard
                          </span>
                        </Link>
                      )}

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <button className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-red-50 text-red-600 transition-colors text-sm">
                            <LogOut className="w-4 h-4 text-red-500" />
                            <span>Log out</span>
                          </button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="max-w-[425px] w-[90vw] rounded-xl">
                          <AlertDialogHeader>
                            <AlertDialogTitle>
                              Are you sure you want to log out?
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              You'll need to sign in again to access your
                              account.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter className="space-x-4">
                            <AlertDialogCancel className="w-full sm:w-auto">
                              Cancel
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={handleSignout}
                              className="w-full sm:w-auto bg-red-600 hover:bg-red-700"
                            >
                              Log out
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                )}
              </div>

              {/* Cart icon for logged-in users (hide on login and signup pages) */}
              {pathname !== "/login" && pathname !== "/signup" && (
                <Link
                  href="/home/<USER>/cart"
                  className="flex items-center justify-center p-2 hover:bg-blue-100 hover:rounded-md transition-colors relative"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <ShoppingCart className="w-6 h-6 text-gray-700" />
                  {cartQuantity > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                      {cartQuantity}
                    </span>
                  )}
                </Link>
              )}
            </>
          ) : (
            <>
              <Link
                href="/login"
                className="bg-primary hover:bg-blue-800 text-white font-bold py-2 px-4 rounded"
                onClick={() => setIsMenuOpen(false)}
              >
                Register/Login
              </Link>
            </>
          )}
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="sm:hidden bg-white">
        {/* First Row: Hamburger Menu, Logo (left) and Cart Icon (right) */}
        <div className="flex items-center justify-between px-4 h-[60px]">
          {/* Left: Hamburger Menu and Logo */}
          <div className="flex items-center gap-3">
            <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild>
                <button
                  className="text-[#131D4D] hover:text-gray-600"
                  aria-label="Toggle menu"
                >
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 6h16M4 12h16M4 18h16"
                    ></path>
                  </svg>
                </button>
              </SheetTrigger>
              <SheetContent
                side="left"
                className="w-80 p-0 flex flex-col h-full max-h-screen"
              >
                <SheetHeader className="p-6 border-b flex-shrink-0">
                  <div className="flex items-center">
                    <a
                      href="/"
                      onClick={() => setIsMenuOpen(false)}
                      className="cursor-pointer"
                    >
                      <img
                        src="/assets/imgs/logo.svg"
                        alt="mailpallet-logo"
                        width={120}
                        height={40}
                        className="h-auto hover:opacity-80 transition-opacity"
                      />
                    </a>
                  </div>
                </SheetHeader>

                {/* MailPallet Home link */}
                <div className="px-4 py-3 border-b border-gray-200 flex-shrink-0">
                  <Link
                    href="/home/<USER>"
                    className="flex items-center space-x-3 py-2 px-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Home className="w-5 h-5" />
                    <span>MailPallet Home</span>
                  </Link>
                </div>

                {/* Single scrollable area for all content */}
                <div className="flex-1 overflow-y-auto min-h-0">
                  {!isAuthenticated ? (
                    <>
                      {/* Categories section for logged-out users */}
                      <div className="p-4 space-y-3">
                        <div className="space-y-1">
                          {categoryHierarchy.map((category) => (
                            <div key={category.id} className="space-y-1">
                              <div className="flex items-center justify-between">
                                <a
                                  href={`/home/<USER>
                                  className="flex-1 flex items-center py-3 px-2 rounded-lg hover:bg-gray-100 transition-colors group"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <span className="font-medium text-[20px] text-gray-700 group-hover:text-gray-900">
                                    {category.name}
                                  </span>
                                </a>
                                {category.children.length > 0 && (
                                  <button
                                    onClick={() =>
                                      toggleCategoryExpansion(category.id)
                                    }
                                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                                  >
                                    <ChevronRight
                                      className={`w-4 h-4 transition-transform text-gray-600 ${expandedCategories.has(category.id) ? "rotate-90" : ""}`}
                                    />
                                  </button>
                                )}
                              </div>

                              {expandedCategories.has(category.id) &&
                                category.children.length > 0 && (
                                  <div className="pl-6 space-y-1">
                                    {category.children.map((subcategory) => (
                                      <a
                                        key={subcategory.id}
                                        href={`/home/<USER>
                                        className="flex items-center gap-2 py-2 px-2 rounded-lg hover:bg-gray-50 transition-colors group"
                                        onClick={() => setIsMenuOpen(false)}
                                      >
                                        <span className="text-gray-400">•</span>
                                        <span className="text-sm text-gray-700 group-hover:text-gray-900">
                                          {subcategory.name}
                                        </span>
                                      </a>
                                    ))}
                                  </div>
                                )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      {/* Categories section for logged-in users */}
                      <div className="p-4 space-y-3">
                        <div className="space-y-1">
                          {categoryHierarchy.map((category) => (
                            <div key={category.id} className="space-y-1">
                              <div className="flex items-center justify-between">
                                <a
                                  href={`/home/<USER>
                                  className="flex-1 flex items-center py-3 px-2 rounded-lg hover:bg-gray-100 transition-colors group"
                                  onClick={() => setIsMenuOpen(false)}
                                >
                                  <span className="font-medium text-[20px] text-gray-700 group-hover:text-gray-900">
                                    {category.name}
                                  </span>
                                </a>
                                {category.children.length > 0 && (
                                  <button
                                    onClick={() =>
                                      toggleCategoryExpansion(category.id)
                                    }
                                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                                  >
                                    <ChevronRight
                                      className={`w-4 h-4 transition-transform text-gray-600 ${expandedCategories.has(category.id) ? "rotate-90" : ""}`}
                                    />
                                  </button>
                                )}
                              </div>

                              {expandedCategories.has(category.id) &&
                                category.children.length > 0 && (
                                  <div className="pl-6 space-y-1">
                                    {category.children.map((subcategory) => (
                                      <a
                                        key={subcategory.id}
                                        href={`/home/<USER>
                                        className="flex items-center gap-2 py-2 px-2 rounded-lg hover:bg-gray-50 transition-colors group"
                                        onClick={() => setIsMenuOpen(false)}
                                      >
                                        <span className="text-gray-400">•</span>
                                        <span className="text-sm text-gray-700 group-hover:text-gray-900">
                                          {subcategory.name}
                                        </span>
                                      </a>
                                    ))}
                                  </div>
                                )}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* My Account section with visual divider */}
                      <div className="border-t-2 border-gray-300 bg-gradient-to-b from-gray-50 to-white mt-4">
                        {/* Section Header */}
                        <div className="px-6 py-4 bg-white border-b border-gray-100">
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-8 h-8">
                              <AvatarFallback className="bg-blue-600 text-white font-bold text-xs">
                                {displayInitials}
                              </AvatarFallback>
                            </Avatar>
                            <h3 className="text-base font-semibold text-gray-900">
                              My Account
                            </h3>
                          </div>
                        </div>

                        {/* Account Links */}
                        <div className="py-3 px-4 space-y-1">
                          <Link
                            href="/home/<USER>"
                            className="flex items-center space-x-4 py-3 px-4 rounded-xl hover:bg-white hover:shadow-sm transition-all duration-200 group"
                            onClick={() => setIsMenuOpen(false)}
                          >
                            <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                              <User className="w-4 h-4 text-gray-600 group-hover:text-blue-600" />
                            </div>
                            <div className="flex-1">
                              <span className="text-sm font-medium text-gray-900 group-hover:text-blue-900">
                                Profile
                              </span>
                              <p className="text-xs text-gray-500">
                                Manage your personal information
                              </p>
                            </div>
                          </Link>

                          <Link
                            href="/home/<USER>/my-orders"
                            className="flex items-center space-x-4 py-3 px-4 rounded-xl hover:bg-white hover:shadow-sm transition-all duration-200 group"
                            onClick={() => setIsMenuOpen(false)}
                          >
                            <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                              <Container className="w-4 h-4 text-gray-600 group-hover:text-blue-600" />
                            </div>
                            <div className="flex-1">
                              <span className="text-sm font-medium text-gray-900 group-hover:text-blue-900">
                                My Orders
                              </span>
                              <p className="text-xs text-gray-500">
                                Track your order history
                              </p>
                            </div>
                          </Link>

                          {isAdmin && (
                            <Link
                              href="/admin/dashboard"
                              className="flex items-center space-x-4 py-3 px-4 rounded-xl hover:bg-green-50 hover:shadow-sm transition-all duration-200 group border border-green-100 mt-2"
                              onClick={() => setIsMenuOpen(false)}
                            >
                              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                                <Shield className="w-4 h-4 text-green-600" />
                              </div>
                              <div className="flex-1">
                                <span className="text-sm font-semibold text-green-700 group-hover:text-green-800">
                                  Admin Dashboard
                                </span>
                                <p className="text-xs text-green-600">
                                  Administrative controls
                                </p>
                              </div>
                            </Link>
                          )}

                          {/* Logout Button */}
                          <div className="pt-2 mt-2 border-t border-gray-200">
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <button className="w-full flex items-center space-x-4 py-3 px-4 rounded-xl hover:bg-red-50 hover:shadow-sm transition-all duration-200 group">
                                  <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors">
                                    <LogOut className="w-4 h-4 text-red-600" />
                                  </div>
                                  <div className="flex-1 text-left">
                                    <span className="text-sm font-medium text-red-700 group-hover:text-red-800">
                                      Log out
                                    </span>
                                    <p className="text-xs text-red-500">
                                      Sign out of your account
                                    </p>
                                  </div>
                                </button>
                              </AlertDialogTrigger>
                              <AlertDialogContent className="max-w-[425px] w-[90vw] rounded-xl">
                                <AlertDialogHeader>
                                  <AlertDialogTitle>
                                    Are you sure you want to log out?
                                  </AlertDialogTitle>
                                  <AlertDialogDescription>
                                    You'll need to sign in again to access your
                                    account.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter className="space-x-4">
                                  <AlertDialogCancel className="w-full sm:w-auto">
                                    Cancel
                                  </AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={handleSignout}
                                    className="w-full sm:w-auto bg-red-600 hover:bg-red-700"
                                  >
                                    Log out
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>

                {/* Bottom button - Register/Login for logged-out users only */}
                {!isAuthenticated && (
                  <div className="p-4 border-t flex-shrink-0">
                    <a
                      href="/login"
                      className="flex items-center justify-center py-3 px-4 bg-primary hover:bg-blue-800 text-white font-bold rounded-lg transition-colors w-full"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <span className="font-medium">Register/Login</span>
                    </a>
                  </div>
                )}
              </SheetContent>
            </Sheet>

            <a href="/" onClick={() => setIsMenuOpen(false)}>
              <img
                src="/assets/imgs/logo.svg"
                alt="mailpallet-logo"
                width={100}
                height={30}
                className="h-auto"
              />
            </a>
          </div>

          {/* Right: Cart Icon (hide on login and signup pages, only show for logged-in users) */}
          {pathname !== "/login" &&
            pathname !== "/signup" &&
            isAuthenticated && (
              <div className="flex items-center">
                <div className="relative profile-dropdown-container hidden">
                  <div
                    onClick={toggleProfileDropdown}
                    className="cursor-pointer"
                    role="button"
                    tabIndex={0}
                    aria-expanded={isProfileDropdownOpen}
                    onKeyDown={(e) =>
                      e.key === "Enter" && toggleProfileDropdown()
                    }
                  >
                    <Avatar className="w-8 h-8 hover:ring-2 hover:ring-blue-300 transition-all">
                      <AvatarFallback className="bg-blue-600 text-white font-bold text-xs">
                        {displayInitials}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                </div>

                {/* Cart for logged-in users (mobile) */}
                <div className="flex flex-col items-center">
                  <Link
                    href="/home/<USER>/cart"
                    className="flex items-center justify-center p-2 hover:bg-blue-100 hover:rounded-md transition-colors relative"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <ShoppingCart className="w-6 h-6 text-[#131D4D]" />
                    {cartQuantity > 0 && (
                      <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                        {cartQuantity}
                      </span>
                    )}
                  </Link>
                </div>
              </div>
            )}
        </div>

        {/* Second Row: Search Bar Container - Always present to maintain consistent height */}
        <div className="px-4 h-[52px] flex items-center">
          {pathname !== "/login" &&
            pathname !== "/signup" &&
            pathname !== "/home/<USER>" &&
            pathname !== "/home/<USER>/my-orders" &&
            pathname !== "/home/<USER>" && (
              <div className="flex bg-white rounded-lg border border-gray-300 shadow-sm overflow-hidden h-10 w-full">
                <Input
                  type="text"
                  placeholder="Search..."
                  className="flex-1 border-0 focus:ring-0 focus:outline-none bg-white text-gray-800 text-sm px-3 min-w-0 h-full"
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch(true)}
                />
                <Button
                  className="bg-primary hover:bg-blue-800 text-white border-0 px-4 rounded-none h-full flex items-center justify-center"
                  onClick={() => handleSearch()}
                >
                  <Search className="w-4 h-4" />
                </Button>
              </div>
            )}
        </div>
      </div>
    </header>
  );
}
